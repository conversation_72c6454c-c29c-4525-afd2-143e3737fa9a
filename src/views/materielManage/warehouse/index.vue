<!-- 仓库管理 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="rowAdd"
          v-has-permi="['materielManage:warehouse:add']"
          >新增</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['materielManage:warehouse:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询</el-button
          >
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置</el-button
          >
        </div>
      </template>
      <template #headIds="{params}">
        <el-select
          v-model="params.headIds"
          placeholder="可多选，选择后将影响仓库确认收货人的数据范畴"
          filterable
          multiple
          value-key="userId"
          style="width: 100%;"
        >
          <el-option
            v-for="i in userOptions"
            :key="i.value"
            :label="i.label"
            :value="i"
          />
        </el-select>
      </template>
      <template #dept="{params}">
        <treeselect
          v-model="params.deptId"
          :options="deptOptions"
          placeholder="请选择组织"
          :default-expand-level="1"
          :normalizer="normalizer"
        />
      </template>
      <template #statusChange="{ row }">
        <el-switch
          v-model="row.status"
          active-value="0"
          inactive-value="1"
          @change="handleStatusChange(row)"
          :disabled="!checkPermission(['materielManage:warehouse:status'])"
        >
        </el-switch>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.5"
      maxSizeText="500m"
    >
    </BatchUpload>
    <DetailDrawer ref="detailDrawer"></DetailDrawer>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/materielManage/warehouse.js";
import Timeline from "@/components/Timeline/index.vue";
import exportMixin from "@/mixin/export.js";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { initParams } from "@/utils/buse.js";
import DetailDrawer from "./components/detailDrawer.vue";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listAllUser, queryCityTree, listDept } from "@/api/common.js";
import { regionData } from "element-china-area-data";
import { queryLog } from "@/api/common.js";

import Treeselect from "@riophae/vue-treeselect";
import { init } from "echarts";
export default {
  name: "materialPage",
  components: { Timeline, BatchUpload, DetailDrawer, Treeselect },
  mixins: [exportMixin],
  data() {
    return {
      uploadObj: {
        api: "/export/report/importStaffInfo",
        url: "/charging-maintenance-ui/static/人员档案批量导入模板.xlsx",
        extraData: {},
      },
      materielTypeOptions: [],
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "repositoryName",
          title: "仓库名称",
          width: 120,
        },
        {
          field: "repositoryCategory",
          title: "仓库类别",
          width: 150,
        },
        {
          field: "deptName",
          title: "所属组织",
          width: 120,
        },
        {
          field: "getCity",
          title: "所在城市",
          width: 120,
        },
        {
          field: "address",
          title: "详细地址",
          width: 150,
        },
        {
          field: "head",
          title: "仓储负责人",
          width: 150,
          formatter: ({ cellValue }) => {
            return cellValue
              ? JSON.parse(cellValue)
                  ?.map((x) => x.nickName)
                  ?.join(",")
              : cellValue;
          },
        },
        {
          field: "phone",
          title: "联系电话",
          width: 120,
        },
        {
          field: "inventoryQuantity",
          title: "库存数量（个）",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.handleDetail(row),
                  }}
                >
                  {row.inventoryQuantity || 0}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "status",
          title: "状态",
          width: 150,
          slots: {
            default: "statusChange",
          },
        },
        {
          field: "createName",
          title: "创建人",
          width: 100,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
        },
        {
          field: "updateName",
          title: "修改人",
          width: 100,
        },
        {
          field: "updateTime",
          title: "修改时间",
          width: 150,
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      recordList: [],
      unitOptions: [],
      userOptions: [],
      regionData: regionData,
      deptOptions: [],
      operationType: "add",
    };
  },
  computed: {
    filterOptions() {
      return {
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        config: [
          {
            field: "repositoryName",
            element: "el-input",
            title: "仓库名称",
            props: {
              placeholder: "请输入仓库名称",
            },
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { label: "停用", value: "1" },
                { label: "启用", value: "0" },
              ],
              placeholder: "请选择状态",
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        modalWidth: "50%",
        addTitle: "新增仓库",
        editTitle: "编辑仓库",
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["materielManage:warehouse:edit"]),
        delBtn: checkPermission(["materielManage:warehouse:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        formConfig: [
          {
            field: "repositoryName",
            title: "仓库名称",
            element: "el-input",
            attrs: {
              maxlength: 100,
              placeholder: "请输入仓库名称",
            },
            rules: [
              { required: true, message: "请输入仓库名称", trigger: "blur" },
              { max: 100, message: "仓库名称最多100个字符", trigger: "blur" },
              {
                validator: this.validateRepositoryName,
                trigger: "blur",
              },
            ],
          },
          {
            field: "repositoryCategory",
            title: "仓库类别",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "queryWarehouseType");
              },
              placeholder: "请输入仓库类别",
            },
            attrs: {
              maxlength: 100,
            },
            rules: [
              { required: true, message: "请输入仓库类别", trigger: "blur" },
              { max: 100, message: "仓库类别最多100个字符", trigger: "blur" },
            ],
          },
          {
            field: "deptId",
            title: "所属组织",
            element: "slot",
            slotName: "dept",
            rules: [
              { required: true, message: "请选择所属组织", trigger: "change" },
            ],
          },
          {
            field: "region",
            title: "所在城市",
            element: "el-cascader",
            props: {
              options: this.regionData?.map((x) => {
                return { ...x, disabled: true };
              }),
              props: {
                checkStrictly: true,
                multiple: false,
                // value: "areaCode",
                // label: "areaName",
              },
              placeholder: "请选择省市区",
              clearable: true,
              filterable: true,
            },
            rules: [
              { required: true, message: "请选择所在城市", trigger: "change" },
            ],
          },
          {
            field: "address",
            title: "详细地址",
            element: "el-input",
            attrs: {
              maxlength: 1000,
              placeholder: "请输入详细地址",
            },
            rules: [
              { required: true, message: "请输入详细地址", trigger: "blur" },
              { max: 1000, message: "详细地址最多1000个字符", trigger: "blur" },
            ],
          },
          {
            field: "headIds",
            element: "slot",
            slotName: "headIds",
            title: "负责人",
            props: {
              options: this.userOptions,
              multiple: true,
              filterable: true,
              placeholder: "可多选，选择后将影响仓库确认收货人的数据范畴",
            },
            rules: [
              { required: true, message: "请选择负责人", trigger: "change" },
            ],
          },
          {
            field: "phone",
            title: "联系电话",
            element: "el-input",
            attrs: {
              maxlength: 20,
              placeholder: "请输入联系电话",
            },
            rules: [
              { required: true, message: "请输入联系电话", trigger: "blur" },
              {
                pattern: /^(1[3-9]\d{9})$|^(\d{3,4}-\d{7,8})$/,
                message: "请输入正确的手机号或座机号格式",
                trigger: "blur",
              },
            ],
          },
        ],
        customOperationTypes: [
          {
            title: "日志",
            modalTitle: "操作日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["materielManage:warehouse:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
    this.getDicts("materiel_type").then((response) => {
      this.materielTypeOptions = response.data;
    });
    this.getDicts("material_unit").then((response) => {
      this.unitOptions = response?.data;
    });
    this.getTreeselect();
    this.listAllUser();
    // this.getCityRegionData();
    this.loadData();
  },
  methods: {
    checkPermission,
    handleDetail(row) {
      this.$refs.detailDrawer.open(row);
    },
    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    querySearch(queryString, cb, apiName) {
      api[apiName]({
        type: "repositoryCategory",
        repositoryCategory: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    // 仓库名称唯一性验证
    async validateRepositoryName(_rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      try {
        const params = {
          repositoryName: value,
          type: this.operationType === "update" ? "UPDATE" : "INSERT",
        };

        // 如果是编辑模式，需要排除当前记录
        const formData = this.$refs.crud?.getFormFields();
        if (formData?.repositoryId) {
          params.repositoryId = formData.repositoryId;
        }

        const res = await api.checkRepositoryName(params);
        if (res.data == "T") {
          callback(new Error("仓库名称已存在，请重新输入"));
        } else {
          callback();
        }
      } catch (error) {
        console.error("仓库名称校验失败:", error);
        callback();
      }
    },
    async loadData() {
      this.loading = true;
      try {
        const res = await api.getTableData({
          ...this.params,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
        });
        this.tableData = res.data || [];
        this.tablePage.total = res.total || 0;
      } finally {
        this.loading = false;
      }
    },
    async handleStatusChange(row) {
      const text = row.status == "0" ? "启用" : "停用";
      this.$confirm(`是否确认${text}？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          api
            .changeStatus({
              repositoryId: row.repositoryId,
              status: row.status,
            })
            .then((res) => {
              if (res.code == "10000") {
                this.$message.success(text + "成功");
                this.loadData();
              } else {
                row.status = row.status == "1" ? "0" : "1";
              }
            })
            .catch(() => {
              row.status = row.status == "1" ? "0" : "1";
            });
        })
        .catch(() => {
          row.status = row.status == "1" ? "0" : "1";
        });
    },
    handleLog(row) {
      queryLog({ businessId: row.repositoryNo }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    rowAdd() {
      this.operationType = "add";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.operationType = "update";
      // 处理编辑时的数据回显
      const { getCity, ...editData } = row;

      // 处理城市数据回显
      if (row.province && row.city) {
        editData.region = [row.province, row.city];
        if (row.county) {
          editData.region.push(row.county);
        }
      }

      // 处理负责人数据回显
      if (row.head) {
        // 假设head字段存储的是用户ID，用逗号分隔
        editData.headIds = row.head ? JSON.parse(row.head) : [];
      }

      this.$refs.crud.switchModalView(true, "UPDATE", editData);
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };

      // 处理城市数据
      if (params.region && params.region.length > 0) {
        params.province = params.region[0];
        params.city = params.region[1];

        if (params.region.length > 2) {
          params.county = params.region[2];
        }
        delete params.region;
      }

      // 处理负责人数据
      if (params.headIds && params.headIds.length > 0) {
        params.head = JSON.stringify(params.headIds);
        delete params.headIds;
      }

      // 处理组织数据
      if (params.deptId) {
        const deptInfo = this.findDeptInfo(params.deptId);
        if (deptInfo) {
          params.deptName = deptInfo.deptName;
        }
      }
      console.log("到这", params, crudOperationType);
      return new Promise(async (resolve) => {
        try {
          let res;
          if (crudOperationType === "add") {
            res = await api.add(params);
          } else if (crudOperationType === "update") {
            res = await api.update(params);
          }

          if (res?.code === "10000") {
            this.$message.success(
              crudOperationType === "ADD" ? "新增成功" : "更新成功"
            );
            this.loadData();
            resolve(true);
          } else {
            this.$message.error(res?.message || "1操作失败");
            resolve(false);
          }
        } catch (error) {
          console.error("2操作失败:", error);
          this.$message.error("2操作失败");
          resolve(false);
        }
      });
    },
    async deleteRowHandler(row) {
      try {
        await this.$confirm("是否确认删除该仓库?", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const res = await api.del(row.repositoryId);
        if (res.code === "10000") {
          this.loadData();
          this.$message.success("删除成功");
        } else {
          this.$message.error(res.message || "删除失败");
        }
      } catch (error) {
        if (error !== "cancel") {
          console.error("删除失败:", error);
        }
      }
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleExport() {
      const params = {
        ...this.params,
      };
      this.handleCommonExport(api.export, params);
    },
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
        console.log(this.deptOptions, "deptOptions");
      });
    },
    // 处理树形结构
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    //去除children为空的children字段
    cleanTree(arr) {
      return arr.map((item) => {
        const newItem = { ...item };
        if (newItem.children) {
          newItem.children = this.cleanTree(newItem.children);
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }
        return newItem;
      });
    },
    cleanRegionTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanRegionTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = res.data;
      });
    },
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },
    // 查找部门信息
    findDeptInfo(deptId) {
      const findInTree = (nodes, targetId) => {
        for (const node of nodes) {
          if (node.deptId === targetId) {
            return node;
          }
          if (node.children) {
            const found = findInTree(node.children, targetId);
            if (found) return found;
          }
        }
        return null;
      };
      return findInTree(this.deptOptions, deptId);
    },
  },
};
</script>

<style lang="less" scoped>
.card-container {
  padding: 20px;
  background: #fff;
  height: 100%;
}
.btn-wrap {
  text-align: right;
  padding: 0 20px 20px;
}

.page-center {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 24px;
  background: #eff6f4;
  font-size: 14px;
  .title {
    font-size: 18px;
  }
  .count {
    font-size: 20px;
    color: red;
  }
  .unit {
    color: red;
  }
}
</style>
