<!-- 物料调拨管理 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchAllocate"
          v-has-permi="['materielManage:allocate:add']"
          >批量调拨</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['materielManage:allocate:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询</el-button
          >
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置</el-button
          >
        </div>
      </template>
    </BuseCrud>

    <!-- 批量调拨抽屉 -->
    <AllocateDrawer ref="allocateDrawer" @success="handleQuery" />

    <!-- 审核弹窗 -->
    <el-dialog
      title="审核调拨单"
      :visible.sync="auditDialogVisible"
      width="500px"
      :before-close="handleAuditDialogClose"
    >
      <el-form
        ref="auditForm"
        :model="auditForm"
        :rules="auditRules"
        label-width="120px"
      >
        <el-form-item label="审核结果" prop="auditResult">
          <el-radio-group v-model="auditForm.auditResult">
            <el-radio label="pass">通过</el-radio>
            <el-radio label="reject">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="不通过原因"
          prop="rejectReason"
          v-if="auditForm.auditResult === 'reject'"
        >
          <el-input
            type="textarea"
            v-model="auditForm.rejectReason"
            placeholder="请输入不通过原因，1000个字符以内"
            :maxlength="1000"
            show-word-limit
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleAuditDialogClose">取消</el-button>
        <el-button type="primary" @click="handleAuditConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from "@/api/materielManage/allocate.js";
import AllocateDrawer from "./components/AllocateDrawer.vue";
import checkPermission from "@/utils/permission.js";
import { operStatusDict } from "@/views/materielManage/config.js";

export default {
  name: "MaterialAllocate",
  components: {
    AllocateDrawer,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      auditDialogVisible: false,
      currentAuditRow: null,
      auditForm: {
        auditResult: "pass",
        rejectReason: "",
      },
      auditRules: {
        auditResult: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
        rejectReason: [
          {
            required: true,
            message: "请输入不通过原因",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (this.auditForm.auditResult === "reject" && !value) {
                callback(new Error("请输入不通过原因"));
              } else {
                callback();
              }
            },
          },
        ],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        spanMethod: this.mergeRowMethod,
        rowConfig: {
          keyField: "quantityId",
          isCurrent: true,
        },
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
      },

      warehouseOptions: [],
      outFlowOptions: [],
      outboundCategoryOptions: [],
      inboundCategoryOptions: [],
      unitOptions: [],
    };
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "operNo",
          title: "调拨单号",
          width: 150,
        },
        {
          field: "materialName",
          title: "物料名称",
          width: 150,
        },
        {
          field: "materialNo",
          title: "物料编码",
          width: 120,
        },
        {
          field: "materialModel",
          title: "规格型号",
          width: 120,
        },
        {
          field: "unit",
          title: "单位",
          width: 80,
          formatter: ({ cellValue }) => {
            console.log(cellValue, this.unitOptions);
            return (
              this.unitOptions?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "boundNum",
          title: "调拨数量",
          width: 100,
        },
        {
          field: "unitPrice",
          title: "单价（元）",
          width: 100,
        },
        {
          field: "materialFactory",
          title: "生产厂家",
          width: 150,
        },
        {
          field: "operStatus",
          title: "状态",
          width: 100,
          formatter: ({ cellValue }) => {
            return (
              operStatusDict.find((x) => x.value == cellValue)?.label ||
              cellValue
            );
          },
        },
        {
          field: "repositoryName",
          title: "出库仓库",
          width: 120,
        },
        {
          field: "outCategory",
          title: "出库类别",
          width: 120,
          formatter: ({ cellValue }) => {
            return (
              this.outboundCategoryOptions?.find(
                (x) => x.dictValue == cellValue
              )?.dictLabel || cellValue
            );
          },
        },
        {
          field: "remark",
          title: "调拨原因",
          width: 150,
        },
        {
          field: "inRepositoryName",
          title: "入库仓库",
          width: 120,
        },
        {
          field: "inCategory",
          title: "入库仓库类别",
          width: 120,
          formatter: ({ cellValue }) => {
            return (
              this.inboundCategoryOptions?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "operUser",
          title: "调拨申请人",
          width: 120,
        },
        {
          field: "operTime",
          title: "调拨申请时间",
          width: 150,
        },
        {
          field: "receiver",
          title: "收件人",
          width: 100,
        },
        {
          field: "receiveAddress",
          title: "收件地址",
          width: 200,
        },
        {
          field: "trackingNum",
          title: "快递单号",
          width: 150,
        },
        {
          field: "auditUser",
          title: "审核人",
          width: 100,
        },
        {
          field: "auditTime",
          title: "审核时间",
          width: 150,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        config: [
          {
            field: "operNo",
            element: "el-input",
            title: "调拨单号",
            props: {
              placeholder: "请输入调拨单号",
            },
          },
          {
            field: "materialName",
            element: "el-input",
            title: "物料名称",
            props: {
              placeholder: "请输入物料名称",
            },
          },
          {
            field: "materialFactory",
            element: "el-input",
            title: "生产厂家",
            props: {
              placeholder: "请输入生产厂家",
            },
          },
          {
            field: "operStatus",
            element: "el-select",
            title: "状态",
            props: {
              placeholder: "请选择状态",
              options: operStatusDict,
            },
          },
          {
            field: "operTimeRange",
            element: "el-date-picker",
            title: "出库时间",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "repositoryName",
            element: "el-input",
            title: "出库仓库",
            props: {
              placeholder: "请输入出库仓库",
            },
          },
          {
            field: "outCategory",
            element: "el-select",
            title: "出库类别",
            props: {
              options: this.outboundCategoryOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择出库类别",
              filterable: true,
            },
          },
          {
            field: "outFlow",
            element: "el-select",
            title: "出库流向",
            props: {
              options: this.outFlowOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择出库流向",
              filterable: true,
            },
          },
          {
            field: "operUser",
            element: "el-input",
            title: "调拨申请人",
            props: {
              placeholder: "请输入调拨申请人",
            },
          },
          {
            field: "trackingNum",
            element: "el-input",
            title: "快递单号",
            props: {
              placeholder: "请输入快递单号",
            },
          },
          {
            field: "receiver",
            element: "el-input",
            title: "收件人",
            props: {
              placeholder: "请输入收件人",
            },
          },
          {
            field: "inRepositoryName",
            element: "el-select",
            title: "流向的仓库",
            props: {
              options: this.warehouseOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择流向的仓库",
              filterable: true,
            },
          },
          {
            field: "materialNo",
            element: "el-input",
            title: "物料编码",
            props: {
              placeholder: "请输入物料编码",
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        modalWidth: "50%",
        addTitle: "新增调拨",
        editTitle: "编辑调拨",
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["materielManage:allocate:edit"]),
        delBtn: checkPermission(["materielManage:allocate:delete"]),
        menu: true,
        menuWidth: 300,
        menuFixed: "right",
        formConfig: [],
        customOperationTypes: [
          {
            title: "审核",
            typeName: "audit",
            event: (row) => {
              return this.handleAudit(row);
            },
            condition: (row) => {
              return (
                row.operStatus === "5" &&
                checkPermission(["materielManage:allocate:audit"])
              );
            },
          },
          {
            title: "确认收货",
            typeName: "confirmReceive",
            event: (row) => {
              return this.handleConfirmReceive(row);
            },
            condition: (row) => {
              return (
                row.operStatus === "6" &&
                checkPermission(["materielManage:allocate:confirm"])
              );
            },
          },
        ],
      };
    },
    params() {
      return {
        operNo: "",
        materialName: "",
        materialNo: "",
        materialModel: "",
        materialFactory: "",
        operStatus: "",
        repositoryName: "",
        operUser: "",
      };
    },
  },
  async created() {
    this.loadWarehouseOptions();
    // 获取字典数据
    const [
      outboundCategoryRes,
      outFlowRes,
      inboundTypeRes,
      materialUnitRes,
    ] = await Promise.all([
      this.getDicts("wl_outbound_type"),
      this.getDicts("wl_out_flow"),
      this.getDicts("wl_inbound_type"),
      this.getDicts("wl_material_unit"),
    ]);
    this.inboundCategoryOptions = inboundTypeRes.data;
    this.unitOptions = materialUnitRes.data;
    this.outboundCategoryOptions = outboundCategoryRes.data;
    this.outFlowOptions = outFlowRes.data;
    this.loadData();
  },
  methods: {
    checkPermission,
    // 加载仓库选项
    async loadWarehouseOptions() {
      try {
        const res = await api.queryWarehouseList();
        this.warehouseOptions =
          res.data?.map((item) => ({
            label: item.repositoryName,
            value: item.repositoryName,
          })) || [];
      } catch (error) {
        console.error("加载仓库选项失败:", error);
      }
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "operTimeRange",
          title: "操作时间",
          startFieldName: "operTimeStart",
          endFieldName: "operTimeEnd",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        let params = {
          ...this.params,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
        };

        this.handleTimeRange(params);

        const res = await api.getTableData(params);
        this.tableData = res.data || [];
        this.tablePage.total = res.total || 0;
      } catch (error) {
        this.$message.error(`加载数据失败：${error.message || "网络错误"}`);
      } finally {
        this.loading = false;
      }
    },

    // 行合并方法（相同调拨单号的行合并指定列）
    mergeRowMethod({ row, column, rowIndex }) {
      const mergeFields = [
        "operNo",
        "operStatus",
        "outCategory",
        "remark",
        "inRepositoryName",
        "inCategory",
        "operUser",
        "operTime",
        "receiver",
        "receiveAddress",
        "trackingNum",
        "auditUser",
        "auditTime",
      ];

      if (mergeFields.includes(column.field) || column.title === "操作") {
        const currentOperNo = row.operNo;
        let rowspan = 1;
        let colspan = 1;

        // 向上查找相同调拨单号的行
        for (let i = rowIndex - 1; i >= 0; i--) {
          if (this.tableData[i].operNo === currentOperNo) {
            return { rowspan: 0, colspan: 0 };
          } else {
            break;
          }
        }

        // 向下查找相同调拨单号的行
        for (let i = rowIndex + 1; i < this.tableData.length; i++) {
          if (this.tableData[i].operNo === currentOperNo) {
            rowspan++;
          } else {
            break;
          }
        }

        return { rowspan, colspan };
      }
    },

    // 批量调拨
    handleBatchAllocate() {
      this.$refs.allocateDrawer.open();
    },

    // 编辑行
    rowEdit(row) {
      this.$refs.allocateDrawer.open(row);
    },

    // 删除行
    async deleteRowHandler(row) {
      try {
        await this.$confirm(
          `确认删除调拨单号"${row.operNo}"的物料"${row.materialName}"吗？\n\n删除后无法恢复！`,
          "警告",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );

        await api.remove({ boundId: row.boundId });
        this.$message.success("删除成功");
        this.loadData();
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error(`删除失败：${error.message || "网络错误"}`);
        }
      }
    },

    // 审核
    handleAudit(row) {
      this.currentAuditRow = row;
      this.auditForm = {
        auditResult: "pass",
        rejectReason: "",
      };
      this.auditDialogVisible = true;
    },

    // 审核弹窗关闭
    handleAuditDialogClose() {
      this.auditDialogVisible = false;
      this.currentAuditRow = null;
      this.$refs.auditForm.resetFields();
    },

    // 审核确认
    async handleAuditConfirm() {
      try {
        await this.$refs.auditForm.validate();

        const params = {
          boundId: this.currentAuditRow.boundId,
          operStatus: this.auditForm.auditResult === "pass" ? "6" : "7", // 6-审核通过，7-审核不通过
          approveReason: this.auditForm.rejectReason || "",
        };

        await api.audit(params);
        this.$message.success("审核成功");
        this.handleAuditDialogClose();
        this.loadData();
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error(`审核失败：${error.message || "网络错误"}`);
        }
      }
    },

    // 确认收货
    async handleConfirmReceive(row) {
      try {
        await this.$confirm(
          `确定已收到调拨单号"${row.operNo}"的物料"${row.materialName}"吗？`,
          "确认收货",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );

        await api.confirmReceive({ boundId: row.boundId });
        this.$message.success("确认收货成功");
        this.loadData();
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error(`确认收货失败：${error.message || "网络错误"}`);
        }
      }
    },

    // 查询
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    // 重置
    handleReset() {
      Object.keys(this.params).forEach((key) => {
        if (Array.isArray(this.params[key])) {
          this.params[key] = [];
        } else {
          this.params[key] = "";
        }
      });
      this.handleQuery();
    },

    // 导出
    async handleExport() {
      try {
        let params = {
          ...this.params,
        };

        this.handleTimeRange(params);

        await api.export(params);
        this.$message.success("导出成功");
      } catch (error) {
        this.$message.error(`导出失败：${error.message || "网络错误"}`);
      }
    },

    // 模态框确认（暂时不需要）
    modalConfirmHandler() {
      // 暂时不需要实现
    },
  },
};
</script>

<style scoped>
.card-container {
  padding: 20px;
}

.btn-wrap {
  display: flex;
  gap: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
