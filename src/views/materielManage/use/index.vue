<!-- 物料使用情况 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <!-- <el-button
          type="primary"
          @click="rowAdd"
          v-has-permi="['archive:list:add']"
          >新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['archive:list:batchAdd']"
          >导入</el-button
        > -->
      </template>
      <template #pageCenter>
        <div class="page-center">
          <div class="title">汇总：</div>
          <div v-for="(item, index) in summaryList" :key="index">
            {{ item.title }}：<span class="count">{{ item.value || 0 }}</span
            ><span class="unit">{{ item.unit }}</span>
          </div>
        </div>
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['materielManage:use:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/materielManage/use.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
export default {
  name: "archivePage",
  components: {},
  mixins: [exportMixin],
  data() {
    return {
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        // toolbarConfig: {
        //   custom: true,
        //   slots: {
        //     buttons: "toolbar_buttons",
        //   },
        // },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "name",
          title: "物料消耗类型",
          width: 150,
        },
        {
          field: "mobile",
          title: "物料名称",
          width: 150,
        },
        {
          field: "wechat",
          title: "物料编码",
          width: 150,
        },
        {
          field: "companyName",
          title: "数量",
          width: 120,
        },
        {
          field: "deptName",
          title: "规格型号",
          width: 150,
        },
        {
          field: "position",
          title: "回收仓库",
          width: 150,
        },
        {
          field: "regionStr",
          title: "可用状态",
          width: 150,
        },

        {
          field: "remark",
          title: "备注",
          width: 150,
        },
        {
          field: "industryType",
          title: "工单编号",
          width: 150,
        },
        {
          field: "industryType",
          title: "工单类型",
          width: 150,
        },
        {
          field: "industryType",
          title: "工单处理人",
          width: 150,
        },
        {
          field: "industryType",
          title: "工单处理时间",
          width: 150,
        },
        {
          field: "industryType",
          title: "场站名称",
          width: 150,
        },
        {
          field: "industryType",
          title: "场站编码",
          width: 150,
        },
        {
          field: "industryType",
          title: "设备名称",
          width: 150,
        },
        {
          field: "industryType",
          title: "设备编码",
          width: 150,
        },
        {
          field: "industryType",
          title: "业务类型",
          width: 150,
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      orderTypeOptions: [],
      consumeOptions: [],
      warehouseOptions: [],
      statusOptions: [],
    };
  },
  computed: {
    summaryList() {
      return [
        {
          title: "物料回收",
          value: "200",
          unit: "个",
        },
        {
          title: "物料使用",
          value: "200",
          unit: "个",
        },
        {
          title: "好件",
          value: "200",
          unit: "个",
        },
        {
          title: "旧件",
          value: "200",
          unit: "个",
        },
        {
          title: "坏件",
          value: "200",
          unit: "个",
        },
        {
          title: "工单",
          value: "200",
          unit: "个",
        },
        {
          title: "场站",
          value: "200",
          unit: "个",
        },
        {
          title: "设备",
          value: "200",
          unit: "个",
        },
      ];
    },
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "name",
            element: "el-input",
            title: "物料名称",
          },
          {
            field: "mobile",
            element: "el-input",
            title: "物料编码",
          },
          {
            field: "position",
            title: "工单类型",
            element: "el-select",
            props: {
              options: this.orderTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "position",
            title: "物料消耗类型",
            element: "el-select",
            props: {
              options: this.consumeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "position",
            title: "回收仓库",
            element: "el-select",
            props: {
              options: this.warehouseOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "name",
            element: "el-input",
            title: "处理人",
          },
          {
            field: "name",
            element: "el-input",
            title: "规格型号",
          },
          {
            field: "position",
            title: "可用状态",
            element: "el-select",
            props: {
              options: this.statusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "name",
            element: "el-input",
            title: "设备名称",
          },
          {
            field: "name",
            element: "el-input",
            title: "场站名称",
          },
          {
            field: "handleTime",
            title: "处理时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "name",
            element: "el-input",
            title: "工单编号",
          },
          {
            field: "name",
            element: "el-input",
            title: "设备编码",
          },
          {
            field: "name",
            element: "el-input",
            title: "场站编码",
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增",
        editBtn: false,
        editTitle: "编辑",
        delBtn: false,
        menu: false,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
    this.loadData();
  },
  activated() {
    this.loadData();
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,

    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleExport() {
      const params = {
        ...this.params,
      };
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "handleTime",
          title: "处理时间",
          startFieldName: "createStartTime",
          endFieldName: "createEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.page-center {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 24px;
  background: #eff6f4;
  font-size: 14px;
  .title {
    font-size: 18px;
  }
  .count {
    font-size: 20px;
    color: red;
  }
  .unit {
    color: red;
  }
}
</style>
