import request from "@/utils/request";

export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/wlRepositoryInfo/queryByPage",
      method: "post",
      data: data,
    });
  },

  // 删除
  del(repositoryId) {
    return request({
      url: "/wlRepositoryInfo/remove",
      method: "post",
      data: { repositoryId },
    });
  },

  // 导出
  export(data) {
    return request({
      url: "/wlRepositoryInfo/export",
      method: "post",
      data: data,
    });
  },

  // 新增
  add(data) {
    return request({
      url: "/wlRepositoryInfo/save",
      method: "post",
      data: data,
    });
  },

  // 更新
  update(data) {
    return request({
      url: "/wlRepositoryInfo/update",
      method: "post",
      data: data,
    });
  },

  // 状态切换
  changeStatus(data) {
    return request({
      url: "/wlRepositoryInfo/changeStatus",
      method: "post",
      data: data,
    });
  },

  // 仓库名称校验
  checkRepositoryName(data) {
    return request({
      url: "/wlRepositoryInfo/checkRepositoryName",
      method: "post",
      data: data,
    });
  },

  // 获取仓库详情
  getDetail(data) {
    return request({
      url: "/wlRepositoryInfo/detail",
      method: "post",
      data: data,
    });
  },

  // 获取已存储过的仓库类别
  queryWarehouseType(data) {
    return request({
      url: "/wlRepositoryInfo/getHistoryType",
      method: "post",
      data: data,
    });
  },

  // 获取可用仓库信息
  getRepository(data) {
    return request({
      url: "/wlRepositoryInfo/getRepository",
      method: "post",
      data: data,
    });
  },
  //查询库存数量明细列表
  getDetailList(data) {
    return request({
      url: "/wlInventoryQuantityDetail/queryByPage",
      method: "post",
      data: data,
    });
  },
  //导出库存数量明细列表
  exportDetailList(data) {
    return request({
      url: "/wlInventoryQuantityDetail/export",
      method: "post",
      data: data,
    });
  },
};
